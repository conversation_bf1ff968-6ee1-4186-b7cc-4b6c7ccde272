/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    GSM.h
  * @brief   GSM模块阻塞式AT指令控制头文件
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

#ifndef __GSM_H__
#define __GSM_H__

#include <stdint.h>
#include <string.h>
#include <stdio.h>
#include <stdlib.h>

/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

// GSM模块状态定义（新的阻塞式）
typedef enum {
    GSM_OK = 0,
    GSM_ERROR,
    GSM_TIMEOUT,
    GSM_BUSY,
    GSM_DISCONNECTED
} gsm_status_t;

// 兼容原有的状态定义
typedef gsm_status_t GSM_Status_t;

// GSM模块状态
typedef enum {
    GSM_STATE_INIT = 0,   // 初始化状态
    GSM_STATE_READY,      // 就绪状态
    GSM_STATE_CONNECTED,  // 已连接状态
    GSM_STATE_ERROR       // 错误状态
} GSM_State_t;

// GSM模块信息结构体（新的阻塞式）
typedef struct {
    char model[32];           // 模组型号
    char ccid[32];            // CCID号
    uint16_t voltage;         // 电压(mV)
    uint8_t rssi;             // 信号强度
    uint8_t ber;              // 误码率
    uint8_t connected;        // 连接状态
} gsm_info_t;

// 兼容原有的信息结构体
typedef struct {
    char model[32];       // 模块型号
    char ccid[32];        // CCID号
    uint16_t voltage;     // 电压值(mV)
    uint8_t signal;       // 信号强度(0-31)
    uint8_t network_reg;  // 网络注册状态
} GSM_Info_t;

// TCP连接参数结构体
typedef struct {
    char server_ip[16];       // 服务器IP
    uint16_t server_port;     // 服务器端口
    char protocol[8];         // 协议类型 "TCP"/"UDP"
} tcp_config_t;

// 新的阻塞式函数声明
gsm_status_t gsm_init(void);
gsm_status_t gsm_get_model(char* model);
gsm_status_t gsm_get_ccid(char* ccid);
gsm_status_t gsm_get_voltage(uint16_t* voltage);
gsm_status_t gsm_get_signal(uint8_t* rssi, uint8_t* ber);

// 快速连接到默认服务器
gsm_status_t gsm_connect_default_server(void);

// 发送数据（自动计算字节数，等待ZL指令回复）
gsm_status_t gsm_send_data(const char* data_string, char* server_command);

// 获取最后一次服务器ZL指令
gsm_status_t gsm_get_last_server_command(char* command_buffer);
gsm_status_t gsm_get_info(gsm_info_t* info);

// 电源控制函数
void gsm_power_on(void);
void gsm_power_off(void);
void gsm_emergency_shutdown(void);

// 兼容性函数
gsm_status_t gsm_simple_init(void);  // 简化初始化流程

// 底层AT指令函数
gsm_status_t gsm_send_at_command(const char* cmd, char* response, uint32_t timeout);
gsm_status_t gsm_wait_for_response(char* response, uint32_t timeout);
gsm_status_t gsm_tcp_connect(const char* ip, uint16_t port);
gsm_status_t gsm_tcp_close(void);

// 工具函数
void gsm_delay_ms(uint32_t ms);
uint16_t gsm_calculate_data_length(const char* data);

// 兼容原有接口的函数声明
GSM_Status_t GSM_Init(void);
GSM_Status_t GSM_SimpleInit(void);
GSM_Status_t GSM_FullInit(void);
GSM_Status_t GSM_GetCCID(char* ccid);
GSM_Status_t GSM_GetSignal(uint8_t* signal);
GSM_Status_t GSM_SendData(const char* data, uint16_t length);
GSM_Status_t GSM_GetInfo(GSM_Info_t* info);
GSM_State_t GSM_GetState(void);
void GSM_PowerOn(void);
void GSM_PowerOff(void);
void GSM_EmergencyShutdown(void);
GSM_Status_t GSM_CloseServer(void);

// 服务器配置宏定义
#define GSM_SERVER_IP               "************"    // 测试服务器IP地址     http://************:5000/
#define GSM_SERVER_PORT             48085             // 端口号

//#define GSM_SERVER_IP               "**********"    // 公司服务器IP地址
//#define GSM_SERVER_PORT             58085             // 端口号


// 配置参数
#define GSM_RESPONSE_BUFFER_SIZE    512
#define GSM_DEFAULT_TIMEOUT         2000    // 2秒超时(根据AT指令响应快的特点)
#define GSM_CONNECT_TIMEOUT         5000    // 5秒连接超时
#define GSM_SEND_TIMEOUT            2000    // 3秒发送超时
#define GSM_MAX_RETRY_COUNT         3       // 最大重试次数

// 兼容原有的宏定义
#define GSM_RX_BUFFER_SIZE          256
#define GSM_AT_TIMEOUT_MS           2000
#define GSM_AT_RETRY_COUNT          3

// STM32 HAL库相关
#include "stm32l0xx_hal.h"
#include "gpio.h"  // 添加GPIO定义

// LPUART1句柄声明（需要在主程序中定义）
extern UART_HandleTypeDef hlpuart1;

// 串口接口函数声明（基于STM32 HAL库）
void gsm_uart_send_string(const char* str);
void gsm_uart_send_byte(uint8_t byte);
uint8_t gsm_uart_receive_byte(void);
uint8_t gsm_uart_data_available(void);

/* USER CODE BEGIN Prototypes */

/* USER CODE END Prototypes */

#endif /* __GSM_H__ */
